#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/ts-node-dev@2.0.0_@types+node@24.0.10_typescript@5.8.3/node_modules/ts-node-dev/lib/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/ts-node-dev@2.0.0_@types+node@24.0.10_typescript@5.8.3/node_modules/ts-node-dev/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/ts-node-dev@2.0.0_@types+node@24.0.10_typescript@5.8.3/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/ts-node-dev@2.0.0_@types+node@24.0.10_typescript@5.8.3/node_modules/ts-node-dev/lib/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/ts-node-dev@2.0.0_@types+node@24.0.10_typescript@5.8.3/node_modules/ts-node-dev/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/ts-node-dev@2.0.0_@types+node@24.0.10_typescript@5.8.3/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-file-service/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node-dev/lib/bin.js" "$@"
else
  exec node  "$basedir/../ts-node-dev/lib/bin.js" "$@"
fi
