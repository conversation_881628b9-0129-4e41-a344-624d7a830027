{"name": "tsconfig", "version": "7.0.0", "description": "Resole and parse `tsconfig.json`, replicating to TypeScript's behaviour", "main": "dist/tsconfig.js", "typings": "dist/tsconfig.d.ts", "files": ["dist/", "LICENSE"], "scripts": {"lint": "tslint \"src/**/*.ts\"", "build": "npm run build-ts", "build-ts": "rm -rf dist && tsc", "test-spec": "mocha dist/**/*.spec.js -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- dist/**/*.spec.js -R spec --bail", "test": "npm run build && npm run lint && npm run test-cov", "prepublish": "npm run build"}, "repository": {"type": "git", "url": "https://github.com/TypeStrong/tsconfig.git"}, "keywords": ["TypeScript", "compiler", "config", "tsconfig", "json", "resolve"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "contributors": ["basa<PERSON><PERSON>@gmail.com"], "license": "MIT", "bugs": {"url": "https://github.com/TypeStrong/tsconfig/issues"}, "homepage": "https://github.com/TypeStrong/tsconfig", "dependencies": {"@types/strip-bom": "^3.0.0", "@types/strip-json-comments": "0.0.30", "strip-bom": "^3.0.0", "strip-json-comments": "^2.0.0"}, "devDependencies": {"@types/chai": "^4.0.4", "@types/mocha": "^2.2.42", "@types/node": "^8.0.25", "bluebird": "^3.4.1", "chai": "^3.0.0", "istanbul": "^0.4.0", "mocha": "^3.2.0", "tslint": "^4.5.1", "tslint-config-standard": "^4.0.0", "typescript": "^2.2.1"}}