{
  "compilerOptions": {
    "target": "es2022" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
    "module": "commonjs" /* Specify what module code is generated. */,
    "outDir": "./dist" /* Redirect output structure to the directory. */,
    "rootDir": "./src" /* Specify the root directory of input files. */,
    "strict": true /* Enable all strict type-checking options. */,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables `allowSyntheticDefaultImports` for type compatibility. */,
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is consistent in imports. */,
    "resolveJsonModule": true /* Enable importing .json files. */,
    "sourceMap": true /* Generate corresponding .map files. */
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules", "dist"]
}
